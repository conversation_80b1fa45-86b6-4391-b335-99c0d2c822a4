<?php

include(dirname(__FILE__) . "/games_funs.php");

require_once './log.php';

use GeoIp2\Database\Reader;

//初始化日志
$logHandler = new CLogFileHandler(sys_get_temp_dir() . DIRECTORY_SEPARATOR . "chip_finger_auth_yz_handgame_" . date('Y-m-d') . '.log');
$log = Log::Init($logHandler, 1);

//---------------- JSON DATA CLASS ---------------------------

class GLResponse
{
    public $msg = 'ok';
    public $ret = 0;
};


$response = new GLResponse();


$product_id = $_POST['device_id'];
$channel = $_POST['channel'];
$hw_finger = $_POST['hw_finger'];

$token = $_POST['token'];

if (isset($REQUEST_PARAMS_RECORD) && $REQUEST_PARAMS_RECORD) {
    Log::DEBUG('REQUEST_PARAMS:' . json_encode($_POST));
}

if (NULL == $product_id || NULL == $channel || NULL == $hw_finger || NULL == $token) {
    output_json_para_error($response);
    exit;
}

$product_id = trim($product_id);
$channel = intval($channel);
$hw_finger = trim($hw_finger);
$token = trim($token);

if (sha1($product_id . $channel . $hw_finger . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

//--------------------------------------------------------

//connect database and query
$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$MYIP = get_ip();

$product_id = mysqli_real_escape_string($gConn, $product_id);
$hw_finger = mysqli_real_escape_string($gConn, $hw_finger);

$gResult = $gConn->query("SELECT channel FROM t_hw_all_channel WHERE channel={$channel}");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $lNum = $gResult->num_rows;

    if ($lNum != 1) {
        output_json_api_error($response, -3, 'channel error!');
        exit;
    }

    $gResult->free();
}

$real_chipid = '';
$finger_salt = '';
$finger_regnum = 0;
$hw_finger_enc = '';
$lHwFinger = '';

$gResult = $gConn->query("SELECT macid,hw_finger_enc,finger_salt,finger_regnum,default_channel FROM t_hw_chip_list WHERE macid_enc='{$product_id}'");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $result_num = $gResult->num_rows;

    if ($result_num > 0) {
        $row = $gResult->fetch_assoc();

        $real_chipid = $row['macid'];
        $finger_salt = $row['finger_salt'];
        if ($row['finger_regnum'] == NULL) {
            output_json_api_error($response, -5, 'Wrong number of activations!');
            exit;
        }
        $finger_regnum = intval($row['finger_regnum']);
        $lHwFinger = $row['hw_finger_enc'];
        $default_channel = $row['default_channel'];
        // if ($default_channel != NULL && $default_channel != 0 && $default_channel != $channel) {
        //     output_json_api_error($response, -3, 'channel error!');
        //     exit;
        // }
    } else {
        Log::DEBUG('CHIP ID ERROR:' . $product_id);
        output_json_api_error($response, -2, 'chip id error!');
        exit;
    }

    $gResult->free();
}

if ($channel == 8034) {
    if (!isHexInRange($real_chipid)) {
        Log::DEBUG('CHIP ID ERROR:' . $product_id . ', channel is 8034, but not in range');
        output_json_api_error($response, -2, 'chip id error!');
        exit;
    }
}

if ($finger_regnum === 0) {
    // 首次激活
    $gConn->begin_transaction();
    $hw_finger_enc = generateEncryptedSalt($hw_finger, $finger_salt);
    $gResult = $gConn->query("UPDATE t_hw_chip_list SET hw_finger='{$hw_finger}',hw_finger_enc='{$hw_finger_enc}',finger_regnum=finger_regnum+1 WHERE macid_enc='{$product_id}'");
    if (!$gResult) {
        output_json_db_error($response);
        $gConn->rollback();
        exit;
    }
    $gResult = $gConn->query("INSERT INTO t_hw_chip_auth_record (chipid, ip, channel, hw_finger, hw_finger_enc) VALUES ('{$real_chipid}', '{$MYIP}', {$channel}, '{$hw_finger}', '{$hw_finger_enc}')");
    if (!$gResult) {
        output_json_db_error($response);
        $gConn->rollback();
        exit;
    }
    $gConn->commit();
} else if ($finger_regnum > 0) {
    if (verifyEncryptedSalt($hw_finger, $finger_salt, $lHwFinger)) {
        // if ($channel == 8016) {
        //     if ($finger_regnum >= 10) {
        //         output_json_api_error($response, -4, 'Activation limit reached!');
        //         exit;
        //     }
        // } else {
        //     if ($finger_regnum >= $DEVICE_FINGER_REG_LIMIT) {
        //         output_json_api_error($response, -4, 'Activation limit reached!');
        //         exit;
        //     }
        // }

        // 查询以往激活记录
        $isExist = 0;
        $gResult = $gConn->query("SELECT hw_finger FROM t_hw_chip_auth_record WHERE chipid='{$real_chipid}'");
        if (!$gResult) {
            output_json_db_error($response);
            $gConn->rollback();
            exit;
        } else {
            $lNum = $gResult->num_rows;

            for ($i = 0; $i < $lNum; $i++) {
                $row = $gResult->fetch_assoc();

                $r_hw_finger = $row['hw_finger'];
                if ($r_hw_finger == $hw_finger) {
                    $isExist = 1;
                    break;
                }
            }

            $gResult->free();
        }

        // 激活新机器
        if ($isExist == 0) {
            $gConn->begin_transaction();
            $hw_finger_enc = generateEncryptedSalt($hw_finger, $finger_salt);
            $gResult = $gConn->query("UPDATE t_hw_chip_list SET hw_finger='{$hw_finger}', hw_finger_enc='{$hw_finger_enc}',finger_regnum=finger_regnum+1 WHERE macid_enc='{$product_id}'");
            if (!$gResult) {
                output_json_db_error($response);
                $gConn->rollback();
                exit;
            }
            $gResult = $gConn->query("INSERT INTO t_hw_chip_auth_record (chipid, ip, channel, hw_finger, hw_finger_enc) VALUES ('{$real_chipid}', '{$MYIP}', {$channel}, '{$hw_finger}', '{$hw_finger_enc}')");
            if (!$gResult) {
                output_json_db_error($response);
                $gConn->rollback();
                exit;
            }
            $gConn->commit();
        }
    } else {
        output_json_api_error($response, -2, 'chip id error!');
        exit;
    }
} else {
    output_json_api_error($response, -5, 'Wrong number of activations!');
    exit;
}


header('Content-type: application/json');
echo json_encode($response);

$gConn->close();



function isHexInRange($input)
{
    // 步骤1: 检查是否为有效的十六进制字符串
    if (!ctype_xdigit($input)) {
        return false; // 不是十六进制字符串，直接返回 false
    }

    // 标准化输入字符串为大写（确保比较时大小写一致）
    $normalizedInput = strtoupper($input);
    $lowerBound = "435047000000000"; // 下界（十六进制）
    $upperBound = "435047000003010"; // 上界（十六进制）

    // 步骤2: 根据长度快速判断
    $length = strlen($normalizedInput);
    if ($length < 15) {
        return false; // 长度小于15，数值上一定小于下界
    }
    if ($length > 15) {
        return false; // 长度大于15，数值上一定大于上界
    }

    // 步骤3: 长度等于15时，进行字符串比较（标准化后，字典序比较等同于数值比较）
    return ($normalizedInput >= $lowerBound && $normalizedInput <= $upperBound);
}
